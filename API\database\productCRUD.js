const db = require('../db');

/**
 * Product CRUD Operations
 * Handles all product-related database operations
 */

// Get all products with brand and category info (with pagination support)
const getAllProducts = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const offset = (page - 1) * limit;

    // Get total count for pagination
    const [countResult] = await db.execute(
      'SELECT COUNT(*) as total FROM products'
    );
    const total = countResult[0].total;

    const query = `
      SELECT p.*, b.name as brand_name, c.name as category_name
      FROM products p
      LEFT JOIN brands b ON p.brand_id = b.id
      LEFT JOIN categories c ON p.category_id = c.id
      ORDER BY p.created_at DESC
      LIMIT ? OFFSET ?
    `;
    const [rows] = await db.execute(query, [limit, offset]);

    // Get photos for each product (optimized with single query)
    if (rows.length > 0) {
      const productIds = rows.map(p => p.id);
      const placeholders = productIds.map(() => '?').join(',');
      const [photoRows] = await db.execute(
        `SELECT * FROM product_photos WHERE product_id IN (${placeholders})`,
        productIds
      );

      // Group photos by product_id
      const photosByProduct = {};
      photoRows.forEach(photo => {
        if (!photosByProduct[photo.product_id]) {
          photosByProduct[photo.product_id] = [];
        }
        photosByProduct[photo.product_id].push(photo);
      });

      // Attach photos to products
      rows.forEach(product => {
        product.photos = photosByProduct[product.id] || [];
      });
    }

    res.json({
      success: true,
      data: rows,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};

// Get product by ID with variants and photos
const getProductById = async (req, res) => {
  try {
    // Get product details
    const [productRows] = await db.execute(`
      SELECT p.*, b.name as brand_name, c.name as category_name
      FROM products p
      LEFT JOIN brands b ON p.brand_id = b.id
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.id = ?
    `, [req.params.id]);

    if (productRows.length === 0) {
      return res.status(404).json({ success: false, message: 'Product not found' });
    }

    // Get product variants
    const [variantRows] = await db.execute(
      'SELECT * FROM product_variants WHERE product_id = ?',
      [req.params.id]
    );

    // Get product photos
    const [photoRows] = await db.execute(
      'SELECT * FROM product_photos WHERE product_id = ?',
      [req.params.id]
    );

    const product = {
      ...productRows[0],
      variants: variantRows,
      photos: photoRows
    };

    res.json({ success: true, data: product });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};

// Create new product
const createProduct = async (req, res) => {
  try {
    const { name, description, price, brand_id, category_id } = req.body;
    const [result] = await db.execute(
      'INSERT INTO products (name, description, price, brand_id, category_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, NOW(), NOW())',
      [name, description, price || 0, brand_id, category_id]
    );
    res.status(201).json({ success: true, id: result.insertId, message: 'Product created successfully' });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};

// Update product
const updateProduct = async (req, res) => {
  try {
    const { name, description, price, brand_id, category_id } = req.body;
    const [result] = await db.execute(
      'UPDATE products SET name = ?, description = ?, price = ?, brand_id = ?, category_id = ?, updated_at = NOW() WHERE id = ?',
      [name, description, price || 0, brand_id, category_id, req.params.id]
    );
    if (result.affectedRows === 0) {
      return res.status(404).json({ success: false, message: 'Product not found' });
    }
    res.json({ success: true, message: 'Product updated successfully' });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};

// Delete product
const deleteProduct = async (req, res) => {
  try {
    const [result] = await db.execute('DELETE FROM products WHERE id = ?', [req.params.id]);
    if (result.affectedRows === 0) {
      return res.status(404).json({ success: false, message: 'Product not found' });
    }
    res.json({ success: true, message: 'Product deleted successfully' });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};

module.exports = {
  getAllProducts,
  getProductById,
  createProduct,
  updateProduct,
  deleteProduct
};
