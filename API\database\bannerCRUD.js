const db = require('../db');

/**
 * Web Banner CRUD Operations
 * Handles all web banner-related database operations
 */

// Get all web banners
const getAllBanners = async (req, res) => {
  try {
    const [rows] = await db.execute('SELECT * FROM web_banners ORDER BY created_at DESC');
    // Map database field names to frontend field names
    const mappedRows = rows.map(row => ({
      ...row,
      image_url: row.banner_image_url,
      link_url: row.redirect_url,
      is_active: row.active
    }));
    res.json({ success: true, data: mappedRows });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};

// Get active web banners only
const getActiveBanners = async (req, res) => {
  try {
    const [rows] = await db.execute('SELECT * FROM web_banners WHERE active = true ORDER BY created_at DESC');
    // Map database field names to frontend field names
    const mappedRows = rows.map(row => ({
      ...row,
      image_url: row.banner_image_url,
      link_url: row.redirect_url,
      is_active: row.active
    }));
    res.json({ success: true, data: mappedRows });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};

// Get banner by ID
const getBannerById = async (req, res) => {
  try {
    const [rows] = await db.execute('SELECT * FROM web_banners WHERE id = ?', [req.params.id]);
    if (rows.length === 0) {
      return res.status(404).json({ success: false, message: 'Banner not found' });
    }
    // Map database field names to frontend field names
    const banner = {
      ...rows[0],
      image_url: rows[0].banner_image_url,
      link_url: rows[0].redirect_url,
      is_active: rows[0].active
    };
    res.json({ success: true, data: banner });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};

// Create new banner
const createBanner = async (req, res) => {
  try {
    const { title, description, image_url, link_url, is_active = true } = req.body;
    const [result] = await db.execute(
      'INSERT INTO web_banners (title, description, banner_image_url, redirect_url, active, created_at, updated_at) VALUES (?, ?, ?, ?, ?, NOW(), NOW())',
      [title || null, description || null, image_url || null, link_url || null, is_active]
    );
    res.status(201).json({ success: true, id: result.insertId, message: 'Banner created successfully' });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};

// Update banner
const updateBanner = async (req, res) => {
  try {
    const { title, description, image_url, link_url, is_active } = req.body;
    const [result] = await db.execute(
      'UPDATE web_banners SET title = ?, description = ?, banner_image_url = ?, redirect_url = ?, active = ?, updated_at = NOW() WHERE id = ?',
      [title || null, description || null, image_url || null, link_url || null, is_active, req.params.id]
    );
    if (result.affectedRows === 0) {
      return res.status(404).json({ success: false, message: 'Banner not found' });
    }
    res.json({ success: true, message: 'Banner updated successfully' });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};

// Delete banner
const deleteBanner = async (req, res) => {
  try {
    const [result] = await db.execute('DELETE FROM web_banners WHERE id = ?', [req.params.id]);
    if (result.affectedRows === 0) {
      return res.status(404).json({ success: false, message: 'Banner not found' });
    }
    res.json({ success: true, message: 'Banner deleted successfully' });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};

module.exports = {
  getAllBanners,
  getActiveBanners,
  getBannerById,
  createBanner,
  updateBanner,
  deleteBanner
};
