const db = require('../db');

/**
 * Brand CRUD Operations
 * Handles all brand-related database operations
 */

// Get all brands
const getAllBrands = async (req, res) => {
  try {
    const [rows] = await db.execute('SELECT * FROM brands ORDER BY created_at DESC');

    // Get photos for each brand (optimized with single query)
    if (rows.length > 0) {
      const brandIds = rows.map(b => b.id);
      const placeholders = brandIds.map(() => '?').join(',');
      const [photoRows] = await db.execute(
        `SELECT * FROM brand_photos WHERE brand_id IN (${placeholders})`,
        brandIds
      );

      // Group photos by brand_id (each brand should have at most one photo)
      const photosByBrand = {};
      photoRows.forEach(photo => {
        photosByBrand[photo.brand_id] = photo;
      });

      // Attach photos to brands
      rows.forEach(brand => {
        brand.photo = photosByBrand[brand.id] || null;
        brand.brand_photo = brand.photo ? brand.photo.photo_url : null; // For backward compatibility
      });
    }

    res.json({ success: true, data: rows });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};

// Get brand by ID
const getBrandById = async (req, res) => {
  try {
    const [rows] = await db.execute('SELECT * FROM brands WHERE id = ?', [req.params.id]);
    if (rows.length === 0) {
      return res.status(404).json({ success: false, message: 'Brand not found' });
    }

    const brand = rows[0];

    // Get photo for this brand
    const [photoRows] = await db.execute('SELECT * FROM brand_photos WHERE brand_id = ?', [req.params.id]);
    brand.photo = photoRows.length > 0 ? photoRows[0] : null;
    brand.brand_photo = brand.photo ? brand.photo.photo_url : null; // For backward compatibility

    res.json({ success: true, data: brand });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};

// Create new brand
const createBrand = async (req, res) => {
  try {
    const { name } = req.body;

    // Check if brand name already exists
    const [existingBrand] = await db.execute('SELECT id FROM brands WHERE name = ?', [name]);
    if (existingBrand.length > 0) {
      return res.status(409).json({ success: false, error: 'Brand name already exists' });
    }

    const [result] = await db.execute(
      'INSERT INTO brands (name, created_at) VALUES (?, NOW())',
      [name]
    );
    res.status(201).json({ success: true, id: result.insertId, message: 'Brand created successfully' });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};

// Update brand
const updateBrand = async (req, res) => {
  try {
    const { name } = req.body;

    // Check if brand name already exists (excluding current brand)
    const [existingBrand] = await db.execute('SELECT id FROM brands WHERE name = ? AND id != ?', [name, req.params.id]);
    if (existingBrand.length > 0) {
      return res.status(409).json({ success: false, error: 'Brand name already exists' });
    }

    const [result] = await db.execute(
      'UPDATE brands SET name = ? WHERE id = ?',
      [name, req.params.id]
    );
    if (result.affectedRows === 0) {
      return res.status(404).json({ success: false, message: 'Brand not found' });
    }
    res.json({ success: true, message: 'Brand updated successfully' });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};

// Delete brand
const deleteBrand = async (req, res) => {
  try {
    // Check if brand is being used by any products
    const [products] = await db.execute('SELECT id FROM products WHERE brand_id = ?', [req.params.id]);
    if (products.length > 0) {
      return res.status(409).json({
        success: false,
        error: 'Cannot delete brand as it is being used by products'
      });
    }

    const [result] = await db.execute('DELETE FROM brands WHERE id = ?', [req.params.id]);
    if (result.affectedRows === 0) {
      return res.status(404).json({ success: false, message: 'Brand not found' });
    }
    res.json({ success: true, message: 'Brand deleted successfully' });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};

module.exports = {
  getAllBrands,
  getBrandById,
  createBrand,
  updateBrand,
  deleteBrand
};