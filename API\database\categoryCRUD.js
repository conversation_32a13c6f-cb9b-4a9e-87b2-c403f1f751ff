const db = require('../db');

/**
 * Category CRUD Operations
 * Handles all category-related database operations
 */

// Get all categories
const getAllCategories = async (req, res) => {
  try {
    const [rows] = await db.execute('SELECT * FROM categories ORDER BY created_at DESC');

    // Get photos for each category (optimized with single query)
    if (rows.length > 0) {
      const categoryIds = rows.map(c => c.id);
      const placeholders = categoryIds.map(() => '?').join(',');
      const [photoRows] = await db.execute(
        `SELECT * FROM category_photos WHERE category_id IN (${placeholders})`,
        categoryIds
      );

      // Group photos by category_id (each category should have at most one photo)
      const photosByCategory = {};
      photoRows.forEach(photo => {
        photosByCategory[photo.category_id] = photo;
      });

      // Attach photos to categories
      rows.forEach(category => {
        category.photo = photosByCategory[category.id] || null;
        category.category_photo = category.photo ? category.photo.photo_url : null; // For backward compatibility
      });
    }

    res.json({ success: true, data: rows });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};

// Get category by ID
const getCategoryById = async (req, res) => {
  try {
    const [rows] = await db.execute('SELECT * FROM categories WHERE id = ?', [req.params.id]);
    if (rows.length === 0) {
      return res.status(404).json({ success: false, message: 'Category not found' });
    }

    const category = rows[0];

    // Get photo for this category
    const [photoRows] = await db.execute('SELECT * FROM category_photos WHERE category_id = ?', [req.params.id]);
    category.photo = photoRows.length > 0 ? photoRows[0] : null;
    category.category_photo = category.photo ? category.photo.photo_url : null; // For backward compatibility

    res.json({ success: true, data: category });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};

// Create new category
const createCategory = async (req, res) => {
  try {
    const { name } = req.body;

    // Check if category name already exists
    const [existingCategory] = await db.execute('SELECT id FROM categories WHERE name = ?', [name]);
    if (existingCategory.length > 0) {
      return res.status(409).json({ success: false, error: 'Category name already exists' });
    }

    const [result] = await db.execute(
      'INSERT INTO categories (name, created_at) VALUES (?, NOW())',
      [name]
    );
    res.status(201).json({ success: true, id: result.insertId, message: 'Category created successfully' });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};

// Update category
const updateCategory = async (req, res) => {
  try {
    const { name } = req.body;

    // Check if category name already exists (excluding current category)
    const [existingCategory] = await db.execute('SELECT id FROM categories WHERE name = ? AND id != ?', [name, req.params.id]);
    if (existingCategory.length > 0) {
      return res.status(409).json({ success: false, error: 'Category name already exists' });
    }

    const [result] = await db.execute(
      'UPDATE categories SET name = ? WHERE id = ?',
      [name, req.params.id]
    );
    if (result.affectedRows === 0) {
      return res.status(404).json({ success: false, message: 'Category not found' });
    }
    res.json({ success: true, message: 'Category updated successfully' });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};

// Delete category
const deleteCategory = async (req, res) => {
  try {
    // Check if category is being used by any products
    const [products] = await db.execute('SELECT id FROM products WHERE category_id = ?', [req.params.id]);
    if (products.length > 0) {
      return res.status(409).json({
        success: false,
        error: 'Cannot delete category as it is being used by products'
      });
    }

    const [result] = await db.execute('DELETE FROM categories WHERE id = ?', [req.params.id]);
    if (result.affectedRows === 0) {
      return res.status(404).json({ success: false, message: 'Category not found' });
    }
    res.json({ success: true, message: 'Category deleted successfully' });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
};

module.exports = {
  getAllCategories,
  getCategoryById,
  createCategory,
  updateCategory,
  deleteCategory
};
